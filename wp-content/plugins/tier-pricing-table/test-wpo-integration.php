<?php
/**
 * Test file for WooCommerce Product Options integration
 * This file can be used to test if the integration is working properly
 * 
 * To use: Add this to your theme's functions.php temporarily or create a simple plugin
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Test function to verify WooCommerce Product Options integration
 */
function test_wpo_tiered_pricing_integration() {
    // Check if both plugins are active
    if ( ! class_exists( 'TierPricingTable\TierPricingTablePlugin' ) ) {
        return 'Tiered Price Table plugin is not active';
    }
    
    if ( ! is_plugin_active( 'woocommerce-product-options/woocommerce-product-options.php' ) ) {
        return 'WooCommerce Product Options plugin is not active';
    }
    
    // Check if our integration class exists
    if ( ! class_exists( 'TierPricingTable\Integrations\Plugins\WooCommerceProductOptions' ) ) {
        return 'WooCommerce Product Options integration class not found';
    }
    
    // Check if the integration is registered
    $integrations = apply_filters( 'tiered_pricing_table/integrations/plugins', array() );
    $wpo_integration_found = false;
    
    foreach ( $integrations as $integration ) {
        if ( $integration === 'TierPricingTable\Integrations\Plugins\WooCommerceProductOptions' ) {
            $wpo_integration_found = true;
            break;
        }
    }
    
    if ( ! $wpo_integration_found ) {
        return 'WooCommerce Product Options integration not registered in plugin list';
    }
    
    return 'Integration appears to be working correctly!';
}

/**
 * Add admin notice to test the integration
 */
function wpo_integration_test_admin_notice() {
    if ( current_user_can( 'manage_options' ) ) {
        $test_result = test_wpo_tiered_pricing_integration();
        echo '<div class="notice notice-info"><p><strong>WPO Integration Test:</strong> ' . esc_html( $test_result ) . '</p></div>';
    }
}

// Uncomment the line below to show the test result in admin
// add_action( 'admin_notices', 'wpo_integration_test_admin_notice' );

/**
 * Test cart item price calculation
 */
function test_wpo_cart_price_calculation( $cart_item ) {
    if ( ! isset( $cart_item['wpo_options'] ) ) {
        return 'No WPO options found in cart item';
    }
    
    // Simulate the integration
    $integration = new TierPricingTable\Integrations\Plugins\WooCommerceProductOptions();
    $base_price = 10.00; // Example base price
    $calculated_price = $integration->addOptionsPrice( $base_price, $cart_item );
    
    return array(
        'base_price' => $base_price,
        'calculated_price' => $calculated_price,
        'options_added' => $calculated_price - $base_price
    );
}
