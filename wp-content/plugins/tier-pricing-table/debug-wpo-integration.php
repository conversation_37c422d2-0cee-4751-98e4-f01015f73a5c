<?php
/**
 * Debug helper for WooCommerce Product Options + Tiered Price Table integration
 * 
 * Add this to your theme's functions.php temporarily to debug the integration
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Add debug information to cart for WPO + TPT integration
 */
function debug_wpo_tpt_integration() {
    if ( ! is_admin() && ( is_cart() || is_checkout() ) ) {
        ?>
        <div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc;">
            <h4>WPO + TPT Integration Debug</h4>
            <?php
            if ( WC()->cart ) {
                foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
                    if ( isset( $cart_item['wpo_options'] ) ) {
                        echo '<div style="margin: 10px 0; padding: 10px; background: white;">';
                        echo '<strong>Product:</strong> ' . $cart_item['data']->get_name() . '<br>';
                        echo '<strong>Quantity:</strong> ' . $cart_item['quantity'] . '<br>';
                        echo '<strong>Base Price:</strong> ' . wc_price( $cart_item['data']->get_price() ) . '<br>';
                        
                        // Check if tiered pricing is active
                        $pricing_rule = \TierPricingTable\PriceManager::getPricingRule( $cart_item['data']->get_id() );
                        $tiered_price = $pricing_rule->getTierPrice( $cart_item['quantity'], false );
                        
                        if ( $tiered_price ) {
                            echo '<strong>Tiered Price:</strong> ' . wc_price( $tiered_price ) . '<br>';
                        }
                        
                        echo '<strong>WPO Options:</strong><br>';
                        foreach ( $cart_item['wpo_options'] as $option_id => $option_data ) {
                            echo '&nbsp;&nbsp;- ' . $option_data['name'] . ': ';
                            if ( isset( $option_data['choice_data'] ) ) {
                                foreach ( $option_data['choice_data'] as $choice ) {
                                    if ( isset( $choice['pricing'] ) ) {
                                        echo $choice['pricing']['type'] . ' = ' . $choice['pricing']['amount'];
                                    }
                                }
                            }
                            echo '<br>';
                        }
                        echo '</div>';
                    }
                }
            }
            ?>
        </div>
        <?php
    }
}

// Uncomment to enable debug display
// add_action( 'woocommerce_before_cart', 'debug_wpo_tpt_integration' );
// add_action( 'woocommerce_before_checkout_form', 'debug_wpo_tpt_integration' );

/**
 * Log cart calculations for debugging
 */
function log_wpo_tpt_calculations( $price, $cart_item ) {
    if ( isset( $cart_item['wpo_options'] ) ) {
        error_log( '=== WPO TPT Debug ===' );
        error_log( 'Product: ' . $cart_item['data']->get_name() );
        error_log( 'Quantity: ' . $cart_item['quantity'] );
        error_log( 'Input Price: ' . $price );
        error_log( 'WPO Options: ' . print_r( $cart_item['wpo_options'], true ) );
        error_log( '===================' );
    }
    return $price;
}

// Uncomment to enable logging
// add_filter( 'tiered_pricing_table/cart/product_cart_price', 'log_wpo_tpt_calculations', 5, 2 );

/**
 * Test function to manually calculate expected prices
 */
function test_wpo_tpt_calculation( $product_id, $quantity, $option_prices = [] ) {
    $product = wc_get_product( $product_id );
    if ( ! $product ) {
        return 'Product not found';
    }
    
    // Get tiered price
    $pricing_rule = \TierPricingTable\PriceManager::getPricingRule( $product_id );
    $tiered_price = $pricing_rule->getTierPrice( $quantity, false );
    
    if ( ! $tiered_price ) {
        $tiered_price = $product->get_price();
    }
    
    // Calculate expected total
    $base_total = $tiered_price * $quantity;
    $options_total = array_sum( $option_prices ) * $quantity;
    $expected_total = $base_total + $options_total;
    
    return [
        'product_name' => $product->get_name(),
        'quantity' => $quantity,
        'tiered_price_per_item' => $tiered_price,
        'base_total' => $base_total,
        'option_prices_per_item' => $option_prices,
        'options_total' => $options_total,
        'expected_total' => $expected_total
    ];
}

// Example usage:
// $result = test_wpo_tpt_calculation( 123, 30, [5.00, 6.00] ); // Product ID 123, 30 items, options 5€ and 6€
// var_dump( $result );
