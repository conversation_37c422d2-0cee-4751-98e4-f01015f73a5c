# WooCommerce Product Options Integration Test Guide

## Overview
This guide explains how to test the new integration between Tier Pricing Table and WooCommerce Product Options (Barn2) that fixes the cart totals issue.

## The Problem
When using both plugins together:
1. Tiered pricing calculates correctly based on quantity ranges
2. But cart totals were missing addon prices from WooCommerce Product Options
3. Only the tiered price was shown, without the addon costs

## The Solution
Created a new integration class `WooCommerceProductOptionsBarn2` that:
1. Detects when WooCommerce Product Options addon prices are present in cart items
2. Adds those addon prices to the tiered pricing calculation
3. Ensures cart totals include both tiered pricing and addon costs

## Testing Steps

### 1. Quick Integration Test
1. Add `?test_integration=1` to any page URL (admin users only)
2. Click "Run Test" in the popup that appears
3. Verify all tests pass:
   - ✅ Integration class loaded
   - ✅ Integration registered
   - ✅ Hooks attached

### 2. Manual Cart Test
1. Create a product with:
   - Tiered pricing rules (e.g., 1-5 items: $10, 6+ items: $8)
   - WooCommerce Product Options addons (e.g., custom text: +$2)

2. Add items to cart with different quantities and addon options

3. Verify cart totals show:
   - Correct tiered price based on quantity
   - Plus addon costs from product options
   - Correct total = (tiered_price + addon_price) × quantity

### 3. Debug Mode Testing
1. Go to WooCommerce → Settings → Tiered Pricing → Feature Flags
2. Enable "Debug" mode
3. View cart page - debug info will show pricing calculations
4. Verify addon prices are included in the calculations

### 4. Mini Cart Testing
1. Add items with addons to cart
2. Check mini cart widget/dropdown
3. Verify prices shown include both tiered pricing and addon costs

## Expected Behavior

### Before Fix
- Cart item price: $8 (tiered price only)
- Missing: $2 addon cost
- Total wrong: $8 × quantity

### After Fix
- Cart item price: $10 ($8 tiered + $2 addon)
- Total correct: $10 × quantity

## Integration Details

### Files Created/Modified
1. **New**: `src/Integrations/Plugins/WooCommerceProductOptionsBarn2.php`
2. **Modified**: `src/Integrations/Integrations.php` (added integration to list)
3. **Test**: `test-integration.php` (debugging tool)

### Hooks Used
- `tiered_pricing_table/cart/product_cart_price` (priority 20)
- `tiered_pricing_table/cart/product_cart_price/item` (priority 20)
- `tiered_pricing_table/cart/product_cart_regular_price/item` (priority 20)
- `tiered_pricing_table/cart/product_cart_old_price` (priority 20)

### Priority Explanation
- WooCommerce Product Options runs at priority 11
- Tier Pricing Table runs at priority 99999
- Our integration runs at priority 20 on TPT hooks to add addon costs back

## Troubleshooting

### Integration Not Working
1. Check if both plugins are active
2. Verify integration is enabled in settings
3. Clear any caches
4. Run the integration test

### Prices Still Wrong
1. Enable debug mode to see calculations
2. Check if addon data is present in cart items (`wpo_options`)
3. Verify tiered pricing rules are configured correctly

### Performance Issues
1. Disable cache temporarily for testing
2. Check for plugin conflicts
3. Monitor server logs for errors

## Support
If issues persist, provide:
1. Integration test results
2. Debug mode output
3. Cart item data structure
4. Plugin versions and configuration
