<?php
/**
 * Cache clearing script for the integration fix
 * Add ?clear_tpt_cache=1 to any page URL to trigger this
 */

// Only run if the parameter is present and user is admin
if (isset($_GET['clear_tpt_cache']) && current_user_can('manage_options')) {
    add_action('init', function() {
        // Clear Tiered Pricing Table cache
        if (class_exists('\TierPricingTable\Core\ServiceContainer')) {
            $container = \TierPricingTable\Core\ServiceContainer::getInstance();
            if ($container && method_exists($container, 'getCache')) {
                $cache = $container->getCache();
                if ($cache && method_exists($cache, 'purge')) {
                    $cache->purge();
                }
            }
        }
        
        // Clear WooCommerce transients
        if (function_exists('wc_delete_product_transients')) {
            wc_delete_product_transients();
        }
        
        // Clear general transients
        delete_transient('tpt_product_data');
        
        // Force cart recalculation
        if (WC()->cart) {
            WC()->cart->calculate_totals();
        }
        
        // Show success message
        add_action('wp_footer', function() {
            echo '<div style="position: fixed; top: 10px; left: 10px; background: #d4edda; border: 2px solid #28a745; color: #155724; padding: 15px; z-index: 9999;">';
            echo '<h4>Cache Cleared Successfully!</h4>';
            echo '<p>✓ Tiered Pricing Table cache cleared</p>';
            echo '<p>✓ WooCommerce product transients cleared</p>';
            echo '<p>✓ Cart totals recalculated</p>';
            echo '</div>';
        });
    });
}
