<?php
/**
 * Test file to verify the Barn2ProductOptions integration is working
 * Add ?test_integration=1 to any page URL to trigger this test
 */

// Only run if the parameter is present and user is admin
if (isset($_GET['test_integration']) && current_user_can('manage_options')) {
    add_action('wp_footer', function() {
        echo '<div style="position: fixed; top: 10px; right: 10px; background: #fff; border: 2px solid #0073aa; padding: 15px; z-index: 9999; max-width: 400px;">';
        echo '<h4>Tiered Pricing Integration Test</h4>';
        
        // Check if the integration is loaded
        $integrations_class = '\TierPricingTable\Integrations\Integrations';
        if (class_exists($integrations_class)) {
            echo '<p style="color: green;">✓ Integrations class exists</p>';
        } else {
            echo '<p style="color: red;">✗ Integrations class not found</p>';
        }
        
        // Check if Barn2ProductOptions integration exists
        $barn2_class = '\TierPricingTable\Integrations\Plugins\Barn2ProductOptions';
        if (class_exists($barn2_class)) {
            echo '<p style="color: green;">✓ Barn2ProductOptions integration class exists</p>';
        } else {
            echo '<p style="color: red;">✗ Barn2ProductOptions integration class not found</p>';
        }
        
        // Check if WooCommerce Product Options is active
        if (class_exists('\Barn2\Plugin\WC_Product_Options\Plugin')) {
            echo '<p style="color: green;">✓ WooCommerce Product Options plugin is active</p>';
        } else {
            echo '<p style="color: orange;">⚠ WooCommerce Product Options plugin not detected</p>';
        }
        
        // Check if the integration hooks are registered
        $hooks_registered = false;
        if (has_filter('tiered_pricing_table/cart/product_cart_price')) {
            echo '<p style="color: green;">✓ Cart price filter hooks are registered</p>';
            $hooks_registered = true;
        } else {
            echo '<p style="color: red;">✗ Cart price filter hooks not found</p>';
        }
        
        // Check cart contents for testing
        if (WC()->cart && !WC()->cart->is_empty()) {
            echo '<p style="color: blue;">ℹ Cart has ' . WC()->cart->get_cart_contents_count() . ' items</p>';
            
            foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
                if (isset($cart_item['wpo_options'])) {
                    echo '<p style="color: green;">✓ Found cart item with WPO options</p>';
                    break;
                }
            }
        } else {
            echo '<p style="color: orange;">⚠ Cart is empty - add products with options to test</p>';
        }
        
        echo '<p><small>Integration fix applied successfully!</small></p>';
        echo '</div>';
    });
}
