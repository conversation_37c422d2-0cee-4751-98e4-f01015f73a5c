<?php
/**
 * Test script for WooCommerce Product Options integration with Tier Pricing Table
 * 
 * Add ?test_integration=1 to any page URL to trigger this test
 * Only works for admin users
 */

// Only run if the parameter is present and user is admin
if (isset($_GET['test_integration']) && current_user_can('manage_options')) {
    add_action('wp_footer', function() {
        ?>
        <div id="tpt-integration-test" style="position: fixed; top: 20px; right: 20px; background: #fff; border: 2px solid #0073aa; padding: 20px; max-width: 400px; z-index: 9999; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h3 style="margin-top: 0;">TPT Integration Test</h3>
            <div id="test-results">
                <p>Testing WooCommerce Product Options integration...</p>
            </div>
            <button onclick="runIntegrationTest()" style="background: #0073aa; color: white; border: none; padding: 10px 15px; cursor: pointer;">Run Test</button>
            <button onclick="document.getElementById('tpt-integration-test').style.display='none'" style="background: #666; color: white; border: none; padding: 10px 15px; cursor: pointer; margin-left: 10px;">Close</button>
        </div>

        <script type="text/javascript">
        function runIntegrationTest() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<p>Running tests...</p>';
            
            let results = [];
            
            // Test 1: Check if our integration class exists
            jQuery.post('<?php echo admin_url('admin-ajax.php'); ?>', {
                action: 'test_tpt_integration',
                test: 'class_exists',
                nonce: '<?php echo wp_create_nonce('test_tpt_integration'); ?>'
            }, function(response) {
                if (response.success) {
                    results.push('✅ Integration class loaded: ' + response.data.message);
                } else {
                    results.push('❌ Integration class error: ' + response.data.message);
                }
                updateResults();
            });
            
            // Test 2: Check if integration is registered
            jQuery.post('<?php echo admin_url('admin-ajax.php'); ?>', {
                action: 'test_tpt_integration',
                test: 'integration_registered',
                nonce: '<?php echo wp_create_nonce('test_tpt_integration'); ?>'
            }, function(response) {
                if (response.success) {
                    results.push('✅ Integration registered: ' + response.data.message);
                } else {
                    results.push('❌ Integration registration error: ' + response.data.message);
                }
                updateResults();
            });
            
            // Test 3: Check if hooks are attached
            jQuery.post('<?php echo admin_url('admin-ajax.php'); ?>', {
                action: 'test_tpt_integration',
                test: 'hooks_attached',
                nonce: '<?php echo wp_create_nonce('test_tpt_integration'); ?>'
            }, function(response) {
                if (response.success) {
                    results.push('✅ Hooks attached: ' + response.data.message);
                } else {
                    results.push('❌ Hooks error: ' + response.data.message);
                }
                updateResults();
            });
            
            function updateResults() {
                resultsDiv.innerHTML = '<div style="font-family: monospace; font-size: 12px;">' + results.join('<br>') + '</div>';
            }
        }
        </script>
        <?php
    });
    
    // Add AJAX handler for the test
    add_action('wp_ajax_test_tpt_integration', function() {
        if (!wp_verify_nonce($_POST['nonce'], 'test_tpt_integration')) {
            wp_die('Security check failed');
        }
        
        $test = sanitize_text_field($_POST['test']);
        
        switch ($test) {
            case 'class_exists':
                if (class_exists('\TierPricingTable\Integrations\Plugins\WooCommerceProductOptionsBarn2')) {
                    wp_send_json_success(['message' => 'WooCommerceProductOptionsBarn2 class found']);
                } else {
                    wp_send_json_error(['message' => 'WooCommerceProductOptionsBarn2 class not found']);
                }
                break;
                
            case 'integration_registered':
                // Check if the integration is in the list
                $integrations = apply_filters('tiered_pricing_table/integrations/plugins', []);
                if (in_array('\TierPricingTable\Integrations\Plugins\WooCommerceProductOptionsBarn2', $integrations)) {
                    wp_send_json_success(['message' => 'Integration found in plugins list']);
                } else {
                    wp_send_json_error(['message' => 'Integration not found in plugins list']);
                }
                break;
                
            case 'hooks_attached':
                // Check if our hooks are attached
                $hooks_attached = [];
                if (has_filter('tiered_pricing_table/cart/product_cart_price')) {
                    $hooks_attached[] = 'product_cart_price';
                }
                if (has_filter('tiered_pricing_table/cart/product_cart_price/item')) {
                    $hooks_attached[] = 'product_cart_price/item';
                }
                
                if (!empty($hooks_attached)) {
                    wp_send_json_success(['message' => 'Hooks found: ' . implode(', ', $hooks_attached)]);
                } else {
                    wp_send_json_error(['message' => 'No hooks found']);
                }
                break;
                
            default:
                wp_send_json_error(['message' => 'Unknown test']);
        }
    });
}
