<?php
/**
 * Temporary test file to force cart refresh
 * Add ?refresh_cart=1 to any page URL to trigger this
 */

// Only run if the parameter is present and user is admin
if (isset($_GET['refresh_cart']) && current_user_can('manage_options')) {
    add_action('wp_footer', function() {
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            console.log('Forcing cart refresh...');
            
            // Method 1: Trigger WooCommerce fragment refresh
            $(document.body).trigger('wc_fragment_refresh');
            
            // Method 2: Clear session storage and force refresh
            if (typeof Storage !== "undefined" && sessionStorage) {
                sessionStorage.removeItem('wc_fragments_' + wc_cart_fragments_params.fragment_name);
                sessionStorage.removeItem(wc_cart_fragments_params.cart_hash_key);
            }
            
            // Method 3: Direct AJAX call to refresh fragments
            setTimeout(function() {
                if (typeof wc_cart_fragments_params !== 'undefined') {
                    $.ajax({
                        url: wc_cart_fragments_params.wc_ajax_url.toString().replace('%%endpoint%%', 'get_refreshed_fragments'),
                        type: 'POST',
                        data: {
                            time: new Date().getTime()
                        },
                        success: function(data) {
                            console.log('Cart fragments refreshed:', data);
                            if (data && data.fragments) {
                                $.each(data.fragments, function(key, value) {
                                    console.log('Updating fragment:', key);
                                    $(key).replaceWith(value);
                                });
                                $(document.body).trigger('wc_fragments_refreshed');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Cart refresh failed:', error);
                        }
                    });
                }
            }, 500);
            
            // Show notification
            $('body').append('<div id="cart-refresh-notice" style="position: fixed; top: 20px; right: 20px; background: #28a745; color: white; padding: 15px; border-radius: 5px; z-index: 9999; font-family: Arial;">Cart refresh triggered! Check mini cart.</div>');
            
            setTimeout(function() {
                $('#cart-refresh-notice').fadeOut();
            }, 3000);
        });
        </script>
        <?php
    });
}
