<?php

// autoload_real.php @generated by Composer

class ComposerAutoloaderInite32795831d49aea4bc9ffacb2e974128
{
    private static $loader;

    public static function loadClassLoader($class)
    {
        if ('Composer\Autoload\ClassLoader' === $class) {
            require __DIR__ . '/ClassLoader.php';
        }
    }

    /**
     * @return \Composer\Autoload\ClassLoader
     */
    public static function getLoader()
    {
        if (null !== self::$loader) {
            return self::$loader;
        }

        spl_autoload_register(array('ComposerAutoloaderInite32795831d49aea4bc9ffacb2e974128', 'loadClassLoader'), true, true);
        self::$loader = $loader = new \Composer\Autoload\ClassLoader(\dirname(__DIR__));
        spl_autoload_unregister(array('ComposerAutoloaderInite32795831d49aea4bc9ffacb2e974128', 'loadClassLoader'));

        require __DIR__ . '/autoload_static.php';
        call_user_func(\Composer\Autoload\ComposerStaticInite32795831d49aea4bc9ffacb2e974128::getInitializer($loader));

        $loader->register(true);

        return $loader;
    }
}
