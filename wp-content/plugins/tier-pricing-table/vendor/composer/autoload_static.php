<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInite32795831d49aea4bc9ffacb2e974128
{
    public static $prefixLengthsPsr4 = array (
        'T' => 
        array (
            'TierPricingTable\\' => 17,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'TierPricingTable\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInite32795831d49aea4bc9ffacb2e974128::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInite32795831d49aea4bc9ffacb2e974128::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInite32795831d49aea4bc9ffacb2e974128::$classMap;

        }, null, ClassLoader::class);
    }
}
