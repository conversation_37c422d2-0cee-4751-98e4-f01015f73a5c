<?php namespace TierPricingTable\Integrations\Plugins;

use TierPricingTable\PriceManager;
use WC_Product;

class AddifyRequestAQuote extends PluginIntegrationAbstract {
	
	public function run() {
		add_filter( 'addify_quote_item_product', function ( WC_Product $product, $quoteItem ) {
			$quantity = $quoteItem['quantity'] ? intval( $quoteItem['quantity'] ) : 1;
			
			$pricingRule = PriceManager::getPricingRule( $product->get_id() );
			
			$price = $pricingRule->getTierPrice( $quantity, false, 'cart' );
			
			if ( $price ) {
				$product->set_price( $price );
			}
			
			return $product;
		}, 10, 2 );
	}
	
	public function getIconURL(): string {
		return $this->getContainer()->getFileManager()->locateAsset( 'admin/integrations/addify-raq-icon.png' );
	}
	
	public function getAuthorURL(): string {
		return 'https://woocommerce.com/products/request-a-quote-plugin-for-woocommerce/';
	}
	
	public function getTitle(): string {
		return __( 'Request a Quote for WooCommerce by Addify', 'tier-pricing-table' );
	}
	
	public function getDescription(): string {
		return __( 'Consider tiered pricing in the request a quote form.', 'tier-pricing-table' );
	}
	
	public function getSlug(): string {
		return 'addify-request-a-quote';
	}
	
	public function getIntegrationCategory(): string {
		return 'other';
	}
}
