<?php namespace TierPricingTable\Integrations\Plugins;

use TierPricingTable\PriceManager;
use WC_Product;

class WooCommerceProductOptionsBarn2 extends PluginIntegrationAbstract {
	
	/**
	 * Add extra addon costs from WooCommerce Product Options (Barn2) to product price in cart.
	 *
	 * @param  float  $price
	 * @param  array  $cart_item
	 *
	 * @return float
	 */
	public function addAddonsPrice( $price, $cart_item ) {
		
		if ( ! $price || ! isset( $cart_item['wpo_options'] ) ) {
			return $price;
		}
		
		$extra_cost = 0;
		$product = $cart_item['data'];
		$quantity = $cart_item['quantity'];
		
		// Get the original product price (before tiered pricing)
		$original_product = wc_get_product( $cart_item['product_id'] );
		$product_price = $original_product ? $original_product->get_price() : 0;
		
		// Calculate addon prices using WooCommerce Product Options logic
		foreach ( $cart_item['wpo_options'] as $option_id => $option_data ) {
			if ( ! isset( $option_data['choice_data'] ) ) {
				continue;
			}
			
			foreach ( $option_data['choice_data'] as $choice_data ) {
				if ( ! isset( $choice_data['pricing'] ) ) {
					continue;
				}
				
				// Use the same calculation method as WooCommerce Product Options
				if ( class_exists( '\Barn2\Plugin\WC_Product_Options\Util\Price' ) ) {
					$extra_cost += \Barn2\Plugin\WC_Product_Options\Util\Price::calculate_option_cart_price( 
						$choice_data['pricing'], 
						$original_product, 
						$quantity, 
						$product_price 
					);
				}
			}
		}
		
		return $price + $extra_cost;
	}

	public function getTitle(): string {
		return __( 'WooCommerce Product Options (by Barn2)', 'tier-pricing-table' );
	}

	public function getDescription(): string {
		return __( 'Make tiered pricing work properly with WooCommerce Product Options addon prices in cart totals.', 'tier-pricing-table' );
	}

	public function getSlug(): string {
		return 'woocommerce-product-options-barn2';
	}

	public function getAuthorURL(): string {
		return 'https://barn2.com/wordpress-plugins/woocommerce-product-options/';
	}

	public function getIconURL(): string {
		return $this->getContainer()->getFileManager()->locateAsset( 'admin/integrations/placeholder.png' );
	}

	public function run() {
		include_once ABSPATH . 'wp-admin/includes/plugin.php';
		
		// Check if WooCommerce Product Options (Barn2) is active
		if ( is_plugin_active( 'woocommerce-product-options/woocommerce-product-options.php' ) || 
			 class_exists( '\Barn2\Plugin\WC_Product_Options\Plugin' ) ) {
			
			// Add addon prices to tiered pricing calculations
			add_filter( 'tiered_pricing_table/cart/product_cart_price', array( $this, 'addAddonsPrice' ), 20, 2 );
			add_filter( 'tiered_pricing_table/cart/product_cart_price/item', array( $this, 'addAddonsPrice' ), 20, 2 );
			add_filter( 'tiered_pricing_table/cart/product_cart_regular_price/item', array( $this, 'addAddonsPrice' ), 20, 2 );
			add_filter( 'tiered_pricing_table/cart/product_cart_old_price', array( $this, 'addAddonsPrice' ), 20, 2 );
		}
	}

	public function getIntegrationCategory(): string {
		return 'product_addons';
	}
}
