<?php namespace TierPricingTable\Integrations\Plugins;

use TierPricingTable\PriceManager;
use WC_Product;

class WooCommerceProductOptions extends PluginIntegrationAbstract {
	
	/**
	 * Add extra product options costs to tiered price in cart (for calculation only).
	 *
	 * @param  float  $price  The tiered price per item
	 * @param  array  $cart_item
	 *
	 * @return float
	 */
	public function addOptionsPriceToTieredPrice( $price, $cart_item ) {

		if ( ! isset( $cart_item['wpo_options'] ) || false == $price ) {
			return $price;
		}

		return $price + $this->calculateOptionsPrice( $cart_item, $price );
	}

	/**
	 * Handle display prices - show base prices only for display contexts
	 *
	 * @param  float  $price  The tiered price per item
	 * @param  array  $cart_item
	 *
	 * @return float
	 */
	public function addOptionsPriceToDisplayPrice( $price, $cart_item ) {

		if ( ! isset( $cart_item['wpo_options'] ) || false == $price ) {
			return $price;
		}

		// For display contexts (mini cart, cart page), show base prices only
		// This ensures the price breakdown shows correct base prices
		return $price;
	}

	/**
	 * DO NOT add options price to old price - keep it as base product price
	 *
	 * @param  float  $price  The old/regular price per item
	 * @param  array  $cart_item
	 *
	 * @return float
	 */
	public function keepOldPriceWithoutOptions( $price, $cart_item ) {
		// Return the old price without adding options
		// This ensures the mini cart shows the correct base prices
		return $price;
	}

	/**
	 * Calculate the total options price per item
	 *
	 * @param  array  $cart_item
	 * @param  float  $base_price
	 *
	 * @return float
	 */
	private function calculateOptionsPrice( $cart_item, $base_price ) {

		// Use WooCommerce Product Options' own calculation method
		if ( ! class_exists( 'Barn2\Plugin\WC_Product_Options\Util\Price' ) ) {
			return 0;
		}

		$product = $cart_item['data'];
		$quantity = $cart_item['quantity'];
		$options_total_per_item = 0;

		foreach ( $cart_item['wpo_options'] as $option_data ) {
			if ( ! isset( $option_data['choice_data'] ) ) {
				continue;
			}

			foreach ( $option_data['choice_data'] as $choice_data ) {
				if ( ! isset( $choice_data['pricing'] ) ) {
					continue;
				}

				// Use WPO's own calculation method to get the per-item option cost
				$option_price_per_item = \Barn2\Plugin\WC_Product_Options\Util\Price::calculate_option_cart_price(
					$choice_data['pricing'],
					$product,
					$quantity,
					$base_price
				);

				$options_total_per_item += $option_price_per_item;
			}
		}

		return $options_total_per_item;
	}
	
	/**
	 * Render compatibility script for frontend price updates
	 */
	public function addCompatibilityScript() {
		?>
		<script>
			(function ($) {
				// Update product options when tiered price changes
				$(document).on('tiered_price_update', function (event, data) {
					// Trigger recalculation of product options pricing
					if (typeof window.wpo_price_calculator !== 'undefined') {
						window.wpo_price_calculator.update_price();
					}
					
					// Update any product option price displays
					$('.wpo-option-price').each(function() {
						var $this = $(this);
						var basePrice = parseFloat($this.data('base-price') || 0);
						var newPrice = data.price;
						
						// Update the base price for percentage calculations
						$this.data('product-price', newPrice);
					});
				});
			})(jQuery);
		</script>
		<?php
	}
	
	public function getTitle(): string {
		return __( 'WooCommerce Product Options (by Barn2)', 'tier-pricing-table' );
	}
	
	public function getDescription(): string {
		return __( 'Make tiered pricing work properly with WooCommerce Product Options plugin.', 'tier-pricing-table' );
	}
	
	public function getSlug(): string {
		return 'woocommerce-product-options';
	}
	
	public function getAuthorURL(): string {
		return 'https://barn2.com/wordpress-plugins/woocommerce-product-options/';
	}
	
	public function getIconURL(): string {
		return $this->getContainer()->getFileManager()->locateAsset( 'admin/integrations/placeholder.png' );
	}
	
	public function run() {
		include_once ABSPATH . 'wp-admin/includes/plugin.php';

		if ( is_plugin_active( 'woocommerce-product-options/woocommerce-product-options.php' ) ) {

			add_action( 'wp_footer', array( $this, 'addCompatibilityScript' ) );

			// Add product options price to tiered pricing calculations (for cart totals)
			add_filter( 'tiered_pricing_table/cart/product_cart_price', array( $this, 'addOptionsPriceToTieredPrice' ), 10, 2 );

			// Handle display separately - don't add options to display prices
			add_filter( 'tiered_pricing_table/cart/product_cart_price/item', array( $this, 'addOptionsPriceToDisplayPrice' ), 10, 2 );

			// DO NOT add options to old price - keep it as base product price for proper mini cart display
			add_filter( 'tiered_pricing_table/cart/product_cart_old_price', array( $this, 'keepOldPriceWithoutOptions' ), 10, 2 );

			// Add simple JavaScript fix for WooCommerce Blocks cart display
			add_action( 'wp_footer', array( $this, 'addSimpleBlocksCartFix' ) );

			// Prevent WPO from overriding tiered pricing in cart
			add_filter( 'wc_product_options_enable_price_calculation', array( $this, 'disableWpoCartCalculation' ), 10, 3 );

			// Handle AJAX price requests for product options
			add_filter( 'wc_product_options_ajax_get_product_price',
				function ( $price, $qty, WC_Product $product ) {

					$pricingRule = PriceManager::getPricingRule( $product->get_id() );
					$newPrice    = $pricingRule->getTierPrice( $qty, false );

					if ( $newPrice ) {
						return $newPrice;
					}

					return $price;
				}, 10, 3 );
		}
	}

	/**
	 * Disable WPO's own price calculation in cart when tiered pricing is active
	 *
	 * @param bool $enable
	 * @param WC_Product $product
	 * @param array $cart_item
	 * @return bool
	 */
	public function disableWpoCartCalculation( $enable, $product, $cart_item ) {
		// Check if this product has tiered pricing rules
		$pricingRule = PriceManager::getPricingRule( $product->get_id() );

		if ( ! empty( $pricingRule->getRules() ) ) {
			// Disable WPO's price calculation, we'll handle it in our integration
			return false;
		}

		return $enable;
	}


	
	public function getIntegrationCategory(): string {
		return 'product_addons';
	}

	/**
	 * Add simple JavaScript fix for WooCommerce Blocks cart price display
	 */
	public function addSimpleBlocksCartFix() {
		if ( ! is_cart() ) {
			return;
		}

		// Get cart data for products with WPO options
		$products_data = array();
		if ( WC()->cart ) {
			foreach ( WC()->cart->get_cart() as $cart_item ) {
				if ( isset( $cart_item['wpo_options'] ) ) {
					$product = $cart_item['data'];
					$pricingRule = PriceManager::getPricingRule( $product->get_id() );
					$tiered_price = $pricingRule->getTierPrice( $cart_item['quantity'], false );

					if ( $tiered_price ) {
						$products_data[] = array(
							'product_id' => $product->get_id(),
							'product_name' => $product->get_name(),
							'tiered_price' => number_format( $tiered_price, 2, ',', '' ),
							'currency_symbol' => get_woocommerce_currency_symbol()
						);
					}
				}
			}
		}

		if ( empty( $products_data ) ) {
			return;
		}
		?>
		<script>
		(function() {
			var productsData = <?php echo json_encode( $products_data ); ?>;

			function fixBlocksCartPrices() {
				productsData.forEach(function(productData) {
					// Find all price elements in the blocks cart
					var priceElements = document.querySelectorAll('.wc-block-components-product-price__value');

					priceElements.forEach(function(priceElement) {
						// Check if this price element belongs to our product
						var cartItem = priceElement.closest('.wc-block-cart-item');
						if (cartItem) {
							// Look for product name or other identifiers
							var productNameElement = cartItem.querySelector('.wc-block-cart-item__product-name');
							if (productNameElement && productNameElement.textContent.includes(productData.product_name)) {
								// Replace the price text
								var newPrice = productData.tiered_price + ' ' + productData.currency_symbol;
								if (priceElement.textContent !== newPrice) {
									priceElement.textContent = newPrice;
								}
							}
						}
					});
				});
			}

			// Run the fix multiple times to catch dynamic updates
			function runFix() {
				fixBlocksCartPrices();
				setTimeout(fixBlocksCartPrices, 500);
				setTimeout(fixBlocksCartPrices, 1000);
				setTimeout(fixBlocksCartPrices, 2000);
			}

			// Run on page load
			if (document.readyState === 'loading') {
				document.addEventListener('DOMContentLoaded', runFix);
			} else {
				runFix();
			}

			// Run when cart updates
			document.addEventListener('wc-blocks_cart_updated', runFix);

			// Observe for changes in the cart area
			var observer = new MutationObserver(function(mutations) {
				var shouldRun = false;
				mutations.forEach(function(mutation) {
					if (mutation.type === 'childList' && mutation.target.classList.contains('wc-block-cart')) {
						shouldRun = true;
					}
				});
				if (shouldRun) {
					setTimeout(fixBlocksCartPrices, 100);
				}
			});

			var cartElement = document.querySelector('.wc-block-cart');
			if (cartElement) {
				observer.observe(cartElement, { childList: true, subtree: true });
			}
		})();
		</script>
		<?php
	}
}
