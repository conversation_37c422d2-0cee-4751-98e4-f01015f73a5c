<?php namespace TierPricingTable\Integrations\Plugins;

use TierPricingTable\PriceManager;
use WC_Product;

class WooCommerceProductOptions extends PluginIntegrationAbstract {
	
	/**
	 * Add extra product options costs to product price in cart.
	 *
	 * @param  float  $price
	 * @param  array  $cart_item
	 *
	 * @return float
	 */
	public function addOptionsPrice( $price, $cart_item ) {
		
		$extra_cost = 0;
		
		if ( isset( $cart_item['wpo_options'] ) && false != $price ) {
			$product = $cart_item['data'];
			$quantity = $cart_item['quantity'];
			
			foreach ( $cart_item['wpo_options'] as $option_data ) {
				if ( ! isset( $option_data['choice_data'] ) ) {
					continue;
				}
				
				foreach ( $option_data['choice_data'] as $choice_data ) {
					if ( ! isset( $choice_data['pricing'] ) ) {
						continue;
					}
					
					$pricing = $choice_data['pricing'];
					$price_type = $pricing['type'];
					$option_price = $pricing['amount'];
					
					switch ( $price_type ) {
						case 'flat_fee':
							$extra_cost += (float) ( $option_price / $quantity );
							break;
						case 'percentage_inc':
							$extra_cost += (float) ( $price * ( $option_price / 100 ) );
							break;
						case 'percentage_dec':
							$extra_cost -= (float) ( $price * ( $option_price / 100 ) );
							break;
						case 'quantity_based':
						case 'price_formula':
							$extra_cost += (float) $option_price;
							break;
						case 'char_count':
							$char_count = isset( $pricing['char_count'] ) ? $pricing['char_count'] : 0;
							$extra_cost += (float) ( $option_price * $char_count );
							break;
						case 'file_count':
							$file_count = isset( $pricing['file_count'] ) ? $pricing['file_count'] : 0;
							$extra_cost += (float) ( $option_price * $file_count );
							break;
						default:
							$extra_cost += (float) $option_price;
							break;
					}
				}
			}
			
			return $price + $extra_cost;
		}
		
		return $price;
	}
	
	/**
	 * Render compatibility script for frontend price updates
	 */
	public function addCompatibilityScript() {
		?>
		<script>
			(function ($) {
				// Update product options when tiered price changes
				$(document).on('tiered_price_update', function (event, data) {
					// Trigger recalculation of product options pricing
					if (typeof window.wpo_price_calculator !== 'undefined') {
						window.wpo_price_calculator.update_price();
					}
					
					// Update any product option price displays
					$('.wpo-option-price').each(function() {
						var $this = $(this);
						var basePrice = parseFloat($this.data('base-price') || 0);
						var newPrice = data.price;
						
						// Update the base price for percentage calculations
						$this.data('product-price', newPrice);
					});
				});
			})(jQuery);
		</script>
		<?php
	}
	
	public function getTitle(): string {
		return __( 'WooCommerce Product Options (by Barn2)', 'tier-pricing-table' );
	}
	
	public function getDescription(): string {
		return __( 'Make tiered pricing work properly with WooCommerce Product Options plugin.', 'tier-pricing-table' );
	}
	
	public function getSlug(): string {
		return 'woocommerce-product-options';
	}
	
	public function getAuthorURL(): string {
		return 'https://barn2.com/wordpress-plugins/woocommerce-product-options/';
	}
	
	public function getIconURL(): string {
		return $this->getContainer()->getFileManager()->locateAsset( 'admin/integrations/placeholder.png' );
	}
	
	public function run() {
		include_once ABSPATH . 'wp-admin/includes/plugin.php';
		
		if ( is_plugin_active( 'woocommerce-product-options/woocommerce-product-options.php' ) ) {
			
			add_action( 'wp_footer', array( $this, 'addCompatibilityScript' ) );
			
			// Add product options price to tiered pricing calculations
			add_filter( 'tiered_pricing_table/cart/product_cart_price', array( $this, 'addOptionsPrice' ), 10, 2 );
			add_filter( 'tiered_pricing_table/cart/product_cart_price/item', array( $this, 'addOptionsPrice' ), 10, 2 );
			add_filter( 'tiered_pricing_table/cart/product_cart_old_price', array( $this, 'addOptionsPrice' ), 10, 2 );
			
			// Handle AJAX price requests for product options
			add_filter( 'wc_product_options_ajax_get_product_price',
				function ( $price, $qty, WC_Product $product ) {
					
					$pricingRule = PriceManager::getPricingRule( $product->get_id() );
					$newPrice    = $pricingRule->getTierPrice( $qty, false );
					
					if ( $newPrice ) {
						return $newPrice;
					}
					
					return $price;
				}, 10, 3 );
		}
	}
	
	public function getIntegrationCategory(): string {
		return 'product_addons';
	}
}
