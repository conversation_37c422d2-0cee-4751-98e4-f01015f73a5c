{"version": 3, "file": "index.css", "mappings": ";;;AACA;EACE;AAAF;;AAIA;;EAAA;AAGA;EACE;AADF;;AAIA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AADF;;AAIA;EACE;EACA;EACA;AADF;;AAIA;EACE;EACA;EACA;EACA;EACA;EACA;AADF;;AAIA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;AADF;;AAIA;EACE;EACA;EACA;EACA;AADF;;AAIA;EACE;EACA;AADF;;AAIA;EACE;EACA;EACA;EACA;EACA;AADF;;AAIA;EACE;EACA;AADF;;AAIA;;EAAA;AAKA;;EAAA;AAIA;EACE;EACA;EACA;EACA;AAJF;;AAOA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAJF;AAME;EACE;EACA;EACA;AAJJ;;AAQA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;AALF;;AAQA;EACE;AALF;;AAQA;EACE;EACA;EACA;EACA;EACA;AALF;;AAQA;EACE;AALF;;AAQA;EACE;EACA;AALF;;AAQA;EACE;AALF;;AAQA;EACE;EACA;EACA;EACA;EACA;EACA;AALF;;AAQA;EACE;AALF;;AAQA;;EAAA,C", "sources": ["webpack://tiered-pricing-block/./src/editor.scss"], "sourcesContent": ["\n.wp-block-create-block-tiered-pricing-block table {\n  border-collapse: collapse;\n}\n\n\n/**\n * Tiered Pricing Options\n */\n.tiered-pricing-options {\n  margin: 20px 0;\n}\n\n.tiered-pricing-option {\n  transition: all .3s;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px;\n  border: 1px solid #E5E5E5;\n  border-radius: 3px;\n  gap: 10px;\n  margin-bottom: 10px;\n}\n\n.tiered-pricing-option__pricing {\n  text-align: right;\n  margin-left: auto;\n  font-weight: bold;\n}\n\n.tiered-pricing-option-checkbox {\n  width: 18px;\n  height: 18px;\n  border-radius: 50%;\n  position: relative;\n  box-sizing: border-box;\n  border: 1px solid #E5E5E5;\n}\n\n.tiered-pricing--active .tiered-pricing-option-checkbox::after {\n  content: \"\";\n  width: 12px;\n  height: 12px;\n  position: absolute;\n  border-radius: 50%;\n  left: 2px;\n  top: 2px;\n}\n\n.tiered-pricing-option-price {\n  display: flex;\n  gap: 5px;\n  align-items: center;\n  justify-content: right;\n}\n\n.tiered-pricing-option-price__original {\n  font-size: .9em;\n  font-weight: normal;\n}\n\n.tiered-pricing-option-total {\n  font-size: .8em;\n  display: none;\n  justify-content: right;\n  align-items: center;\n  gap: 5px;\n}\n\n.tiered-pricing-option-total__original_total {\n  font-weight: normal;\n  font-size: .9em;\n}\n\n/**\n * END Tiered Pricing Options styles\n */\n\n\n/**\n* Tiered pricing select\n */\n\n.tiered-pricing-dropdown {\n  margin: 20px 0;\n  width: 100%;\n  max-width: 800px;\n  position: relative;\n}\n\n.tiered-pricing-dropdown__list {\n  box-sizing: border-box;\n  position: absolute;\n  max-height: 300px;\n  overflow-y: auto;\n  border: 1px solid;\n  border-top: none;\n  width: 100%;\n  background: white;\n  display: none;\n  z-index: 999;\n\n  ul {\n    margin: 0;\n    padding: 0;\n    list-style: none;\n  }\n}\n\n.tiered-pricing-dropdown__select-box {\n  border-radius: 3px;\n  cursor: pointer;\n  border: 1px solid;\n  padding: 5px;\n  align-items: center;\n  display: flex;\n  background: #fff;\n}\n\n.tiered-pricing-dropdown__select-box--active {\n  border-radius: 3px 3px 0 0;\n}\n\n.tiered-pricing-dropdown__select-box-arrow {\n  width: 25px;\n  margin-left: auto;\n  height: 25px;\n  display: flex;\n  transition: all .2s;\n}\n\n.tiered-pricing-dropdown__select-box--active .tiered-pricing-dropdown__select-box-arrow {\n  transform: rotate(180deg);\n}\n\n.tiered-pricing-dropdown-option__pricing {\n  margin-left: auto;\n  font-weight: bold;\n}\n\n.tiered-pricing-option-price {\n  margin-left: auto;\n}\n\n.tiered-pricing-dropdown-option {\n  justify-content: space-between;\n  align-items: center;\n  box-sizing: border-box;\n  padding: 10px;\n  display: flex;\n  width: 100%;\n}\n\n.tiered-pricing-dropdown-option-price__original {\n  font-weight: normal;\n}\n\n/**\nEnd tiered pricing select styles\n */"], "names": [], "sourceRoot": ""}