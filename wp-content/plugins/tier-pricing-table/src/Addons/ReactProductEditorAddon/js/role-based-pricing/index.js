(()=>{"use strict";var e={20:(e,t,i)=>{var r=i(609),n=Symbol.for("react.element"),l=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,s=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,o={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,i){var r,l={},c=null,d=null;for(r in void 0!==i&&(c=""+i),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(d=t.ref),t)a.call(t,r)&&!o.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===l[r]&&(l[r]=t[r]);return{$$typeof:n,type:e,key:c,ref:d,props:l,_owner:s.current}}t.Fragment=l,t.jsx=c,t.jsxs=c},848:(e,t,i)=>{e.exports=i(20)},609:e=>{e.exports=window.React}},t={};const i=window.wp.blocks,r=window.wc.blockTemplates,n=window.wp.components,l=window.wp.coreData,a=window.wp.element,s=window.wp.i18n;var o=function i(r){var n=t[r];if(void 0!==n)return n.exports;var l=t[r]={exports:{}};return e[r](l,l.exports,i),l.exports}(848);function c({pricingType:e,updatePricingType:t,isPremium:i}){return(0,o.jsx)("div",{className:"tiered-pricing-product-editor-row",children:(0,o.jsx)("div",{style:{width:"100%"},children:(0,o.jsx)(n.SelectControl,{label:(0,s.__)("Tiered pricing type","tier-pricing-table"),value:e,options:[{label:i?(0,s.__)("Percentage","tier-pricing-table"):(0,s.__)("Percentage (premium feature)","tier-pricing-table"),value:"percentage"},{label:(0,s.__)("Fixed prices","tier-pricing-table"),value:"fixed"}],onChange:e=>t(e)})})})}function d({data:e,rowIndex:t,updateRow:i}){return(0,o.jsx)("div",{className:"tiered-pricing-product-editor-row",children:(0,o.jsxs)("div",{className:"tiered-pricing-product-editor-columns",children:[(0,o.jsx)("div",{className:"tiered-pricing-product-editor-column tiered-pricing-product-editor-column--quantity",children:(0,o.jsx)(n.BaseControl,{id:"tiered_pricing_percentage_discounts__quantity_"+t,children:(0,o.jsx)(n.__experimentalInputControl,{min:2,step:1,type:"number",value:e.quantity,placeholder:(0,s.__)("Quantity","tier-pricing-table"),onChange:r=>i(t,{quantity:r,value:e.value}),label:(0,s.__)("Quantity","tier-pricing-table")})})}),(0,o.jsx)("div",{className:"tiered-pricing-product-editor-column tiered-pricing-product-editor-column--price",children:(0,o.jsx)(n.BaseControl,{id:"tiered_pricing_percentage_discounts__discount_"+t,children:(0,o.jsx)(n.__experimentalInputControl,{label:(0,s.__)("Percentage discount","tier-pricing-table"),value:e.value,type:"number",prefix:"%",max:100,placeholder:(0,s.__)("Discount","tier-pricing-table"),onChange:r=>i(t,{quantity:e.quantity,value:r})})})}),(0,o.jsx)("div",{style:{alignSelf:"end",flex:"0"},children:(0,o.jsx)(n.Button,{isSmall:!0,onClick:()=>i(t,null),style:{padding:"0px"},children:(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false",children:(0,o.jsx)("path",{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})})})})]})})}function p({tiers:e,updateTiers:t}){e=0===(e=e||[{quantity:null,value:null}]).length?[{quantity:null,value:null}]:e;const i=(i,r)=>{const n=[...e];null===r?(n.splice(i,1),0===n.length&&n.push({quantity:null,value:null})):(r.value=Math.min(100,Number.parseFloat(r.value)),r.quantity=Number.parseFloat(r.quantity),n[i]=r),t(n)};return(0,o.jsx)("div",{children:e.map(((e,t)=>(0,o.jsx)(d,{data:e,rowIndex:t,updateRow:i},t)))})}const u=window.wc.productEditor;function g({data:e,rowIndex:t,updateRow:i}){const r=(0,u.__experimentalUseCurrencyInputProps)({value:e.value,onChange:r=>{i(t,{quantity:e.quantity,value:r})}});return(0,o.jsx)("div",{className:"tiered-pricing-product-editor-row",children:(0,o.jsxs)("div",{className:"tiered-pricing-product-editor-columns",children:[(0,o.jsx)("div",{className:"tiered-pricing-product-editor-column tiered-pricing-product-editor-column--quantity",children:(0,o.jsx)(n.BaseControl,{id:"tiered_pricing_fixed_quantity__quantity_"+t,children:(0,o.jsx)(n.__experimentalInputControl,{min:2,step:1,type:"number",value:e.quantity,placeholder:(0,s.__)("Quantity","tier-pricing-table"),onChange:r=>i(t,{quantity:r,value:e.value}),label:(0,s.__)("Quantity","tier-pricing-table")})})}),(0,o.jsx)("div",{className:"tiered-pricing-product-editor-column tiered-pricing-product-editor-column--price",children:(0,o.jsx)(n.BaseControl,{id:"tiered_pricing_fixed_quantity__discount_"+t,children:(0,o.jsx)(n.__experimentalInputControl,{...r,placeholder:(0,s.__)("Price","tier-pricing-table"),label:(0,s.__)("Price","tier-pricing-table")})})}),(0,o.jsx)("div",{style:{alignSelf:"end",flex:"0"},children:(0,o.jsx)(n.Button,{isSmall:!0,onClick:()=>i(t,null),style:{padding:"0px"},children:(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false",children:(0,o.jsx)("path",{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})})})})]})})}function _({tiers:e,updateTiers:t}){e=0===(e=e||[{quantity:null,value:null}]).length?[{quantity:null,value:null}]:e;const i=(i,r)=>{const n=[...e];if(null===r)return n.splice(i,1),0===n.length&&n.push({quantity:null,value:null}),void t(n);r.quantity=Number.parseInt(r.quantity),n[i]=r,t(n)};return(0,o.jsx)("div",{children:e.map(((e,t)=>(0,o.jsx)(g,{data:e,rowIndex:t,updateRow:i},t)))})}function x({percentageTiers:e,fixedTiers:t,pricingType:i,updatePricingType:r,updatePercentageTiers:l,updateFixedTiers:a,isPremium:d}){const u="percentage"===i?p:_,g="percentage"===i?e:t,x="percentage"===i?l:a,h="percentage"===i&&!d;return(0,o.jsxs)("div",{style:{width:"100%"},children:[(0,o.jsx)(c,{pricingType:i,updatePricingType:r,isPremium:d}),(0,o.jsxs)("div",{style:{pointerEvents:h?"none":"auto",opacity:h?"0.5":"1"},children:[(0,o.jsx)(u,{tiers:g,updateTiers:x}),(0,o.jsx)("div",{className:"tiered-pricing-product-editor-row tiered-pricing-product-editor-row--add-new-quantity",children:(0,o.jsx)(n.Button,{variant:"secondary",onClick:()=>{x([...g,{quantity:"",value:""}])},children:(0,s.__)("Add tier","tier-pricing-table")})})]})]})}const h=e=>{const t={};return e.forEach((e=>{e.quantity&&e.value&&(t[e.quantity]=Number.parseFloat(e.value))})),t};function y(e,t,i){const r=t[e]||{},[n,l]=(0,a.useState)((()=>{const e=Object.keys(r).map((e=>({quantity:e,value:r[e]})));return e.push({quantity:"",value:""}),e}));return[n,r=>{l(r),i(Object.assign({},t,{[e]:h(r)}))}]}const m=function({roleData:e,setRoleData:t}){const[i,r]=y("fixed_tiered_pricing_rules",e,t),[l,a]=y("percentage_tiered_pricing_rules",e,t);return(0,o.jsx)(n.PanelRow,{children:(0,o.jsx)(x,{percentageTiers:l,updatePercentageTiers:a,fixedTiers:i,updateFixedTiers:r,pricingType:e.tiered_pricing_type,updatePricingType:i=>{t(Object.assign({},e,{tiered_pricing_type:i}))}})})},j=function({roleData:e,setRoleData:t}){const i=(0,u.__experimentalUseCurrencyInputProps)({value:e.regular_price,onChange:i=>{t(Object.assign({},e,{regular_price:i}))}}),r=(0,u.__experimentalUseCurrencyInputProps)({value:e.sale_price||"",onChange:i=>{t(Object.assign({},e,{sale_price:i}))}});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(n.PanelRow,{children:(0,o.jsx)(n.SelectControl,{label:"Pricing type",options:[{value:"flat",label:"Flat prices"},{value:"percentage",label:"Percentage discount"}],value:e.pricing_type||"flat",onChange:i=>{t({...e,pricing_type:i})}})}),"flat"===e.pricing_type&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(n.PanelRow,{children:(0,o.jsx)("div",{style:{width:"100%"},children:(0,o.jsx)(n.__experimentalInputControl,{...i,placeholder:"Leave empty to do not change regular price for this role",label:"Regular Price"})})}),(0,o.jsx)(n.PanelRow,{children:(0,o.jsx)("div",{style:{width:"100%"},children:(0,o.jsx)(n.__experimentalInputControl,{...r,placeholder:"Leave empty to do not change sale price for this role",label:"Sale Price"})})})]}),"percentage"===e.pricing_type&&(0,o.jsx)(o.Fragment,{children:(0,o.jsx)(n.PanelRow,{children:(0,o.jsx)("div",{style:{width:"100%"},children:(0,o.jsx)(n.__experimentalInputControl,{value:e.discount,onChange:i=>{t({...e,discount:i})},type:"number",min:0,max:100,prefix:"%",placeholder:"Leave empty to do not add any discount on the base price",label:"Percentage discount"})})})})]})},v=function({roleData:e,setRoleData:t}){return(0,o.jsx)("div",{style:{width:"100%"},children:(0,o.jsx)(n.ToggleControl,{style:{width:"100%"},label:"Enable custom pricing for this role",checked:e.is_enabled,onChange:i=>{t({...e,is_enabled:i})}})})},b=function({roleData:e,setRoleData:t}){return(0,o.jsx)(o.Fragment,{children:(0,o.jsx)(n.PanelRow,{children:(0,o.jsx)("div",{style:{width:"100%"},children:(0,o.jsx)(n.__experimentalInputControl,{value:e.minimum_order_quantity,onChange:i=>{t(Object.assign({},e,{minimum_order_quantity:i}))},placeholder:"Leave empty to do not change regular price for this role",label:"Minimum Order Quantity"})})})})},f=({title:e})=>(0,o.jsxs)("div",{style:{position:"relative",margin:"30px 0"},children:[(0,o.jsx)("hr",{}),(0,o.jsx)("span",{style:{transform:"translate(-50%, -50%)",top:"50%",left:"50%",position:"absolute",background:"#fff",padding:"5px 10px",fontWeight:"500"},children:e})]}),w=({upgradeURL:e})=>(0,o.jsxs)("span",{children:[(0,o.jsx)("span",{style:{color:"red"},children:(0,s.__)("Role-based pricing is a premium feature.","tier-pricing-table")}),(0,o.jsxs)("a",{href:e,target:"_blank",children:[" ",(0,s.__)("Upgrade to unlock","tier-pricing-table")]})]}),P=({roleTitle:e})=>(0,o.jsxs)("span",{children:[e,(0,o.jsx)("span",{style:{background:"#c6e1c6",color:"#5b841b",display:"inline-block",fontSize:".8em",borderRadius:"4px",padding:"4px 8px",marginLeft:"10px"},children:"Enabled"})]}),R=({roleKey:e,roleTitle:t,context:i,isPremium:r,upgradeURL:s})=>{const[c,d]=function(e,t){const[i,r]=(0,l.useEntityProp)("postType",t,"tiered_pricing_roles_data");Object.keys(i).forEach((e=>{i[e].is_enabled=!0}));const[n,s]=(0,a.useState)({...i});return[{is_enabled:n[e]?.is_enabled||!1,pricing_type:n[e]?.pricing_type||"flat",regular_price:n[e]?.regular_price||null,sale_price:n[e]?.sale_price||null,discount:n[e]?.discount||null,discount_type:n[e]?.discount_type||null,tiered_pricing_type:n[e]?.tiered_pricing_type||"fixed",percentage_tiered_pricing_rules:n[e]?.percentage_tiered_pricing_rules||[],fixed_tiered_pricing_rules:n[e]?.fixed_tiered_pricing_rules||[],minimum_order_quantity:n[e]?.minimum_order_quantity||null},t=>{const i={...n,[e]:t},l={};Object.keys(i).forEach((e=>{i[e].is_enabled&&(l[e]=Object.assign({},i[e]))})),s(i),r(l)}]}(e,i.postType),p=c.is_enabled?(0,o.jsx)(P,{roleTitle:t}):t,u=r?"":"tiered-pricing-role-data__hidden";return(0,o.jsxs)(n.PanelBody,{initialOpen:!1,title:p,children:[(0,o.jsx)("div",{style:{margin:"20px 0 30px 0"},children:(0,o.jsx)(n.PanelRow,{children:(0,o.jsx)(v,{roleData:c,setRoleData:d})})}),c.is_enabled&&(0,o.jsxs)("div",{children:[!r&&(0,o.jsx)(w,{upgradeURL:s}),(0,o.jsxs)("div",{className:u,children:[(0,o.jsx)(f,{title:"Base Pricing Options"}),(0,o.jsx)(j,{roleData:c,setRoleData:d}),(0,o.jsx)(f,{title:"Tiered Pricing"}),(0,o.jsx)(m,{roleData:c,setRoleData:d}),(0,o.jsx)(f,{title:"Quantity Options"}),(0,o.jsx)(b,{roleData:c,setRoleData:d})]})]})]})},q=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"tiered-pricing-table/product-editor-role-based-pricing","version":"0.1.0","title":"Woo blocks-based product editor: Role-based pricing","category":"widgets","icon":"flag","description":"Tiered Pricing - Role based pricing","attributes":{"availableRoles":{"type":"object","default":[]},"isPremium":{"type":"boolean","default":true},"upgradeUrl":{"type":"string","default":""}},"supports":{"html":false,"inserter":false},"textdomain":"test-editor","editorScript":"file:./index.js","editorStyle":"file:./index.css","style":"file:./style-index.css"}');(0,i.registerBlockType)(q,{edit:function({attributes:e,context:t}){const i=(0,r.useWooBlockProps)(e),l=e.isPremium||!1,a=e.upgradeURL||"";return e.availableRoles=e.availableRoles||{},(0,o.jsx)("div",{...i,children:(0,o.jsx)(n.Panel,{children:Object.keys(e.availableRoles).map((i=>{const r=e.availableRoles[i]?.name||"Undefined Role";return(0,o.jsx)(R,{roleKey:i,isPremium:l,upgradeURL:a,roleTitle:r,context:t},i)}))})})}})})();