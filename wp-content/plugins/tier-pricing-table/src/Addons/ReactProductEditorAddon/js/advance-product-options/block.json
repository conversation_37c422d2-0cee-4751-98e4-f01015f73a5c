{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "tiered-pricing-table/product-editor-advance-product-options", "version": "0.1.0", "title": "Woo blocks-based product editor: Advanced Product Options", "category": "widgets", "icon": "flag", "description": "A block to allow users to update advanced product options related to the tiered pricing plugin.", "attributes": {"message": {"type": "string", "__experimentalRole": "content", "source": "text", "selector": "div"}, "availableRoles": {"type": "object", "default": []}, "availableLayouts": {"type": "object", "default": []}}, "supports": {"html": false, "inserter": false}, "textdomain": "test-editor", "editorScript": "file:./index.js", "editorStyle": "file:./index.css", "style": "file:./style-index.css"}