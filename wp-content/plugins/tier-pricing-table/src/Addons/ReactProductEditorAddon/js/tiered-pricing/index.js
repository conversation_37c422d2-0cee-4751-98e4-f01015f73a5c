(()=>{"use strict";var e={20:(e,t,i)=>{var r=i(609),n=Symbol.for("react.element"),l=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),a=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,o={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,i){var r,c={},s=null,u=null;for(r in void 0!==i&&(s=""+i),void 0!==t.key&&(s=""+t.key),void 0!==t.ref&&(u=t.ref),t)l.call(t,r)&&!o.hasOwnProperty(r)&&(c[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===c[r]&&(c[r]=t[r]);return{$$typeof:n,type:e,key:s,ref:u,props:c,_owner:a.current}}t.jsx=c,t.jsxs=c},848:(e,t,i)=>{e.exports=i(20)},609:e=>{e.exports=window.React}},t={};const i=window.wp.blocks,r=window.wc.blockTemplates,n=window.wp.coreData,l=window.wp.components,a=window.wp.i18n;var o=function i(r){var n=t[r];if(void 0!==n)return n.exports;var l=t[r]={exports:{}};return e[r](l,l.exports,i),l.exports}(848);function c({pricingType:e,updatePricingType:t,isPremium:i}){return(0,o.jsx)("div",{className:"tiered-pricing-product-editor-row",children:(0,o.jsx)("div",{style:{width:"100%"},children:(0,o.jsx)(l.SelectControl,{label:(0,a.__)("Tiered pricing type","tier-pricing-table"),value:e,options:[{label:i?(0,a.__)("Percentage","tier-pricing-table"):(0,a.__)("Percentage (premium feature)","tier-pricing-table"),value:"percentage"},{label:(0,a.__)("Fixed prices","tier-pricing-table"),value:"fixed"}],onChange:e=>t(e)})})})}function s({data:e,rowIndex:t,updateRow:i}){return(0,o.jsx)("div",{className:"tiered-pricing-product-editor-row",children:(0,o.jsxs)("div",{className:"tiered-pricing-product-editor-columns",children:[(0,o.jsx)("div",{className:"tiered-pricing-product-editor-column tiered-pricing-product-editor-column--quantity",children:(0,o.jsx)(l.BaseControl,{id:"tiered_pricing_percentage_discounts__quantity_"+t,children:(0,o.jsx)(l.__experimentalInputControl,{min:2,step:1,type:"number",value:e.quantity,placeholder:(0,a.__)("Quantity","tier-pricing-table"),onChange:r=>i(t,{quantity:r,value:e.value}),label:(0,a.__)("Quantity","tier-pricing-table")})})}),(0,o.jsx)("div",{className:"tiered-pricing-product-editor-column tiered-pricing-product-editor-column--price",children:(0,o.jsx)(l.BaseControl,{id:"tiered_pricing_percentage_discounts__discount_"+t,children:(0,o.jsx)(l.__experimentalInputControl,{label:(0,a.__)("Percentage discount","tier-pricing-table"),value:e.value,type:"number",prefix:"%",max:100,placeholder:(0,a.__)("Discount","tier-pricing-table"),onChange:r=>i(t,{quantity:e.quantity,value:r})})})}),(0,o.jsx)("div",{style:{alignSelf:"end",flex:"0"},children:(0,o.jsx)(l.Button,{isSmall:!0,onClick:()=>i(t,null),style:{padding:"0px"},children:(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false",children:(0,o.jsx)("path",{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})})})})]})})}function u({tiers:e,updateTiers:t}){e=0===(e=e||[{quantity:null,value:null}]).length?[{quantity:null,value:null}]:e;const i=(i,r)=>{const n=[...e];null===r?(n.splice(i,1),0===n.length&&n.push({quantity:null,value:null})):(r.value=Math.min(100,Number.parseFloat(r.value)),r.quantity=Number.parseFloat(r.quantity),n[i]=r),t(n)};return(0,o.jsx)("div",{children:e.map(((e,t)=>(0,o.jsx)(s,{data:e,rowIndex:t,updateRow:i},t)))})}const d=window.wc.productEditor;function p({data:e,rowIndex:t,updateRow:i}){const r=(0,d.__experimentalUseCurrencyInputProps)({value:e.value,onChange:r=>{i(t,{quantity:e.quantity,value:r})}});return(0,o.jsx)("div",{className:"tiered-pricing-product-editor-row",children:(0,o.jsxs)("div",{className:"tiered-pricing-product-editor-columns",children:[(0,o.jsx)("div",{className:"tiered-pricing-product-editor-column tiered-pricing-product-editor-column--quantity",children:(0,o.jsx)(l.BaseControl,{id:"tiered_pricing_fixed_quantity__quantity_"+t,children:(0,o.jsx)(l.__experimentalInputControl,{min:2,step:1,type:"number",value:e.quantity,placeholder:(0,a.__)("Quantity","tier-pricing-table"),onChange:r=>i(t,{quantity:r,value:e.value}),label:(0,a.__)("Quantity","tier-pricing-table")})})}),(0,o.jsx)("div",{className:"tiered-pricing-product-editor-column tiered-pricing-product-editor-column--price",children:(0,o.jsx)(l.BaseControl,{id:"tiered_pricing_fixed_quantity__discount_"+t,children:(0,o.jsx)(l.__experimentalInputControl,{...r,placeholder:(0,a.__)("Price","tier-pricing-table"),label:(0,a.__)("Price","tier-pricing-table")})})}),(0,o.jsx)("div",{style:{alignSelf:"end",flex:"0"},children:(0,o.jsx)(l.Button,{isSmall:!0,onClick:()=>i(t,null),style:{padding:"0px"},children:(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false",children:(0,o.jsx)("path",{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})})})})]})})}function g({tiers:e,updateTiers:t}){e=0===(e=e||[{quantity:null,value:null}]).length?[{quantity:null,value:null}]:e;const i=(i,r)=>{const n=[...e];if(null===r)return n.splice(i,1),0===n.length&&n.push({quantity:null,value:null}),void t(n);r.quantity=Number.parseInt(r.quantity),n[i]=r,t(n)};return(0,o.jsx)("div",{children:e.map(((e,t)=>(0,o.jsx)(p,{data:e,rowIndex:t,updateRow:i},t)))})}function _({percentageTiers:e,fixedTiers:t,pricingType:i,updatePricingType:r,updatePercentageTiers:n,updateFixedTiers:s,isPremium:d}){const p="percentage"===i?u:g,_="percentage"===i?e:t,x="percentage"===i?n:s,y="percentage"===i&&!d;return(0,o.jsxs)("div",{style:{width:"100%"},children:[(0,o.jsx)(c,{pricingType:i,updatePricingType:r,isPremium:d}),(0,o.jsxs)("div",{style:{pointerEvents:y?"none":"auto",opacity:y?"0.5":"1"},children:[(0,o.jsx)(p,{tiers:_,updateTiers:x}),(0,o.jsx)("div",{className:"tiered-pricing-product-editor-row tiered-pricing-product-editor-row--add-new-quantity",children:(0,o.jsx)(l.Button,{variant:"secondary",onClick:()=>{x([..._,{quantity:"",value:""}])},children:(0,a.__)("Add tier","tier-pricing-table")})})]})]})}const x=window.wp.element;function y(e,t){const[i,r]=(0,n.useEntityProp)("postType",t,"percentage"===e?"tiered_pricing_percentage_rules":"tiered_pricing_fixed_rules"),[l,a]=(0,x.useState)((()=>{const e=Object.keys(i).map((e=>({quantity:e,value:i[e]})));return e.push({quantity:"",value:""}),e}));return[l,e=>{a(e),r((e=>{const t={};return e.forEach((e=>{e.quantity&&e.value&&(t[e.quantity]=Number.parseFloat(e.value))})),t})(e))}]}const m=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"tiered-pricing-table/product-editor-tiered-pricing","version":"0.1.0","title":"Woo blocks-based product editor: Tiered Pricing","category":"widgets","icon":"flag","description":"A block to allow users to input tiered pricing","supports":{"html":false,"inserter":false},"attributes":{"isPremium":{"type":"boolean","default":true}},"textdomain":"tier-pricing-table","editorScript":"file:./index.js","editorStyle":"file:./index.css","style":"file:./style-index.css"}');(0,i.registerBlockType)(m,{edit:function({attributes:e,context:t}){const i=t.postType||"product",[l,a]=y("percentage",i),[c,s]=y("fixed",i),[u,d]=(0,n.useEntityProp)("postType",i,"tiered_pricing_type"),p=(0,r.useWooBlockProps)(e),g=e.isPremium||!1;return(0,o.jsx)("div",{...p,children:(0,o.jsx)(_,{percentageTiers:l,fixedTiers:c,isPremium:g,updatePercentageTiers:a,updateFixedTiers:s,pricingType:u,updatePricingType:d})})}})})();