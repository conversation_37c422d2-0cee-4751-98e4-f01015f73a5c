(()=>{"use strict";var e={20:(e,r,t)=>{var o=t(609),i=Symbol.for("react.element"),s=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),n=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,a={key:!0,ref:!0,__self:!0,__source:!0};r.jsx=function(e,r,t){var o,l={},p=null,c=null;for(o in void 0!==t&&(p=""+t),void 0!==r.key&&(p=""+r.key),void 0!==r.ref&&(c=r.ref),r)s.call(r,o)&&!a.hasOwnProperty(o)&&(l[o]=r[o]);if(e&&e.defaultProps)for(o in r=e.defaultProps)void 0===l[o]&&(l[o]=r[o]);return{$$typeof:i,type:e,key:p,ref:c,props:l,_owner:n.current}}},848:(e,r,t)=>{e.exports=t(20)},609:e=>{e.exports=window.React}},r={};const t=window.wp.blocks,o=window.wc.blockTemplates,i=window.wp.blockEditor;var s=function t(o){var i=r[o];if(void 0!==i)return i.exports;var s=r[o]={exports:{}};return e[o](s,s.exports,t),s.exports}(848);const n=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":2,"name":"tiered-pricing-table/premium-wrapper","title":"Woo blocks-based product editor: Premium Wrapper","category":"tiered-pricing-table","description":"","keywords":["products"],"textdomain":"tier-pricing-table","attributes":{"upgradeUrl":{"type":"string","default":""}},"supports":{"align":false,"html":false,"multiple":true,"reusable":false,"inserter":false,"lock":false,"__experimentalToolbar":false},"editorScript":"file:./index.js"}');(0,t.registerBlockType)(n,{edit:function({attributes:e}){const r=(0,o.useWooBlockProps)(e),t=e.isPremium;return(0,s.jsx)("div",{...r,children:(0,s.jsx)("div",{style:{opacity:t?1:.5,pointerEvents:t?"auto":"none"},children:(0,s.jsx)(i.InnerBlocks,{templateLock:"all"})})})}})})();