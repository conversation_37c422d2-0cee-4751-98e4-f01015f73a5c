[02-Jul-2025 21:18:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-options</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /www/kinsta/public/bottega-del-camice/wp-includes/functions.php on line 6121
[02-Jul-2025 21:18:20 UTC] PHP Stack trace:
[02-Jul-2025 21:18:20 UTC] PHP   1. {main}() /www/kinsta/public/bottega-del-camice/test-tier-pricing-addon-fix.php:0
[02-Jul-2025 21:18:20 UTC] PHP   2. require_once() /www/kinsta/public/bottega-del-camice/test-tier-pricing-addon-fix.php:10
[02-Jul-2025 21:18:20 UTC] PHP   3. require_once() /www/kinsta/public/bottega-del-camice/wp-load.php:50
[02-Jul-2025 21:18:20 UTC] PHP   4. require_once() /www/kinsta/public/bottega-del-camice/wp-config.php:103
[02-Jul-2025 21:18:20 UTC] PHP   5. include_once() /www/kinsta/public/bottega-del-camice/wp-settings.php:545
[02-Jul-2025 21:18:20 UTC] PHP   6. Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Simple_Plugin->register() /www/kinsta/public/bottega-del-camice/wp-content/plugins/woocommerce-product-options/woocommerce-product-options.php:89
[02-Jul-2025 21:18:20 UTC] PHP   7. Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Simple_Plugin->register_services() /www/kinsta/public/bottega-del-camice/wp-content/plugins/woocommerce-product-options/dependencies/barn2/barn2-lib/src/Plugin/Simple_Plugin.php:75
[02-Jul-2025 21:18:20 UTC] PHP   8. Barn2\Plugin\WC_Product_Options\Plugin->add_services() /www/kinsta/public/bottega-del-camice/wp-content/plugins/woocommerce-product-options/dependencies/barn2/barn2-lib/src/Service/Service_Container.php:40
[02-Jul-2025 21:18:20 UTC] PHP   9. Barn2\Plugin\WC_Product_Options\Admin\Wizard\Setup_Wizard->__construct($plugin = class Barn2\Plugin\WC_Product_Options\Plugin { protected $data = ['id' => 461766, 'name' => 'WooCommerce Product Options', 'version' => '2.4.0', 'file' => '/www/kinsta/public/bottega-del-camice/wp-content/plugins/woocommerce-product-options/woocommerce-product-options.php', 'is_woocommerce' => TRUE, 'is_edd' => FALSE, 'documentation_path' => 'kb-categories/product-options-kb/', 'settings_path' => 'edit.php?post_type=product&page=wpo_options', 'wc_features' => [...], 'license_setting_path' => '', 'legacy_db_prefix' => '']; private ${Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Simple_Plugin}basename = NULL; private ${Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Simple_Plugin}dir_path = '/www/kinsta/public/bottega-del-camice/wp-content/plugins/woocommerce-product-options/'; private ${Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Simple_Plugin}dir_url = NULL; private array ${Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Simple_Plugin}services_classes = [0 => 'Barn2\\Plugin\\WC_Product_Options\\Dependencies\\Lib\\Service\\Core_Service', 1 => 'Barn2\\Plugin\\WC_Product_Options\\Dependencies\\Lib\\Service\\Standard_Service', 2 => 'Barn2\\Plugin\\WC_Product_Options\\Dependencies\\Lib\\Service\\Premium_Service']; private array ${Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Simple_Plugin}services = ['plugin_data' => class Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Plugin_Data { ... }, 'notices' => class Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Admin\Notice_Provider { ... }, 'i18n' => class Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\I18n { ... }, 'requirements' => class Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Requirements { ... }, 'wc_compatibility' => class Barn2\Plugin\WC_Product_Options\Dependencies\Lib\WooCommerce\Compatibility { ... }, 'license' => class Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\License\Plugin_License { ... }, 'plugin_updater' => class Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Admin\Plugin_Updater { ... }, 'license_checker' => class Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\License\License_Checker { ... }, 'license_setting' => class Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\License\Admin\License_Key_Setting { ... }, 'license_notices' => class Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\License\Admin\License_Notices { ... }, 'plugin_setup' => class Barn2\Plugin\WC_Product_Options\Plugin_Setup { ... }] }) /www/kinsta/public/bottega-del-camice/wp-content/plugins/woocommerce-product-options/src/Plugin.php:46
[02-Jul-2025 21:18:20 UTC] PHP  10. Barn2\Plugin\WC_Product_Options\Admin\Wizard\Steps\License_Verification->__construct() /www/kinsta/public/bottega-del-camice/wp-content/plugins/woocommerce-product-options/src/Admin/Wizard/Setup_Wizard.php:37
[02-Jul-2025 21:18:20 UTC] PHP  11. Barn2\Plugin\WC_Product_Options\Dependencies\Setup_Wizard\Steps\Welcome->__construct() /www/kinsta/public/bottega-del-camice/wp-content/plugins/woocommerce-product-options/src/Admin/Wizard/Steps/License_Verification.php:21
[02-Jul-2025 21:18:20 UTC] PHP  12. esc_html__($text = 'Welcome', $domain = 'woocommerce-product-options') /www/kinsta/public/bottega-del-camice/wp-content/plugins/woocommerce-product-options/dependencies/barn2/setup-wizard/src/Steps/Welcome.php:25
[02-Jul-2025 21:18:20 UTC] PHP  13. translate($text = 'Welcome', $domain = 'woocommerce-product-options') /www/kinsta/public/bottega-del-camice/wp-includes/l10n.php:340
[02-Jul-2025 21:18:20 UTC] PHP  14. get_translations_for_domain($domain = 'woocommerce-product-options') /www/kinsta/public/bottega-del-camice/wp-includes/l10n.php:195
[02-Jul-2025 21:18:20 UTC] PHP  15. _load_textdomain_just_in_time($domain = 'woocommerce-product-options') /www/kinsta/public/bottega-del-camice/wp-includes/l10n.php:1409
[02-Jul-2025 21:18:20 UTC] PHP  16. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>woocommerce-product-options</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /www/kinsta/public/bottega-del-camice/wp-includes/l10n.php:1379
[02-Jul-2025 21:18:20 UTC] PHP  17. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-options</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added i'..., $error_level = *uninitialized*) /www/kinsta/public/bottega-del-camice/wp-includes/functions.php:6061
[02-Jul-2025 21:18:20 UTC] PHP  18. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-options</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added i'..., $error_level = 1024) /www/kinsta/public/bottega-del-camice/wp-includes/functions.php:6121
[02-Jul-2025 21:18:20 UTC] TPT WPO Integration: WooCommerce Product Options plugin detected, setting up integration hooks
[02-Jul-2025 21:18:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-options</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /www/kinsta/public/bottega-del-camice/wp-includes/functions.php on line 6121
[02-Jul-2025 21:18:21 UTC] PHP Stack trace:
[02-Jul-2025 21:18:21 UTC] PHP   1. {main}() /www/kinsta/public/bottega-del-camice/index.php:0
[02-Jul-2025 21:18:21 UTC] PHP   2. require() /www/kinsta/public/bottega-del-camice/index.php:17
[02-Jul-2025 21:18:21 UTC] PHP   3. require_once() /www/kinsta/public/bottega-del-camice/wp-blog-header.php:13
[02-Jul-2025 21:18:21 UTC] PHP   4. require_once() /www/kinsta/public/bottega-del-camice/wp-load.php:50
[02-Jul-2025 21:18:21 UTC] PHP   5. require_once() /www/kinsta/public/bottega-del-camice/wp-config.php:103
[02-Jul-2025 21:18:21 UTC] PHP   6. include_once() /www/kinsta/public/bottega-del-camice/wp-settings.php:545
[02-Jul-2025 21:18:21 UTC] PHP   7. Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Simple_Plugin->register() /www/kinsta/public/bottega-del-camice/wp-content/plugins/woocommerce-product-options/woocommerce-product-options.php:89
[02-Jul-2025 21:18:21 UTC] PHP   8. Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Simple_Plugin->register_services() /www/kinsta/public/bottega-del-camice/wp-content/plugins/woocommerce-product-options/dependencies/barn2/barn2-lib/src/Plugin/Simple_Plugin.php:75
[02-Jul-2025 21:18:21 UTC] PHP   9. Barn2\Plugin\WC_Product_Options\Plugin->add_services() /www/kinsta/public/bottega-del-camice/wp-content/plugins/woocommerce-product-options/dependencies/barn2/barn2-lib/src/Service/Service_Container.php:40
[02-Jul-2025 21:18:21 UTC] PHP  10. Barn2\Plugin\WC_Product_Options\Admin\Wizard\Setup_Wizard->__construct($plugin = class Barn2\Plugin\WC_Product_Options\Plugin { protected $data = ['id' => 461766, 'name' => 'WooCommerce Product Options', 'version' => '2.4.0', 'file' => '/www/kinsta/public/bottega-del-camice/wp-content/plugins/woocommerce-product-options/woocommerce-product-options.php', 'is_woocommerce' => TRUE, 'is_edd' => FALSE, 'documentation_path' => 'kb-categories/product-options-kb/', 'settings_path' => 'edit.php?post_type=product&page=wpo_options', 'wc_features' => [...], 'license_setting_path' => '', 'legacy_db_prefix' => '']; private ${Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Simple_Plugin}basename = NULL; private ${Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Simple_Plugin}dir_path = '/www/kinsta/public/bottega-del-camice/wp-content/plugins/woocommerce-product-options/'; private ${Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Simple_Plugin}dir_url = NULL; private array ${Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Simple_Plugin}services_classes = [0 => 'Barn2\\Plugin\\WC_Product_Options\\Dependencies\\Lib\\Service\\Core_Service', 1 => 'Barn2\\Plugin\\WC_Product_Options\\Dependencies\\Lib\\Service\\Standard_Service', 2 => 'Barn2\\Plugin\\WC_Product_Options\\Dependencies\\Lib\\Service\\Premium_Service']; private array ${Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Simple_Plugin}services = ['plugin_data' => class Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Plugin_Data { ... }, 'notices' => class Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Admin\Notice_Provider { ... }, 'i18n' => class Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\I18n { ... }, 'requirements' => class Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Requirements { ... }, 'wc_compatibility' => class Barn2\Plugin\WC_Product_Options\Dependencies\Lib\WooCommerce\Compatibility { ... }, 'license' => class Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\License\Plugin_License { ... }, 'plugin_updater' => class Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\Admin\Plugin_Updater { ... }, 'license_checker' => class Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\License\License_Checker { ... }, 'license_setting' => class Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\License\Admin\License_Key_Setting { ... }, 'license_notices' => class Barn2\Plugin\WC_Product_Options\Dependencies\Lib\Plugin\License\Admin\License_Notices { ... }, 'plugin_setup' => class Barn2\Plugin\WC_Product_Options\Plugin_Setup { ... }] }) /www/kinsta/public/bottega-del-camice/wp-content/plugins/woocommerce-product-options/src/Plugin.php:46
[02-Jul-2025 21:18:21 UTC] PHP  11. Barn2\Plugin\WC_Product_Options\Admin\Wizard\Steps\License_Verification->__construct() /www/kinsta/public/bottega-del-camice/wp-content/plugins/woocommerce-product-options/src/Admin/Wizard/Setup_Wizard.php:37
[02-Jul-2025 21:18:21 UTC] PHP  12. Barn2\Plugin\WC_Product_Options\Dependencies\Setup_Wizard\Steps\Welcome->__construct() /www/kinsta/public/bottega-del-camice/wp-content/plugins/woocommerce-product-options/src/Admin/Wizard/Steps/License_Verification.php:21
[02-Jul-2025 21:18:21 UTC] PHP  13. esc_html__($text = 'Welcome', $domain = 'woocommerce-product-options') /www/kinsta/public/bottega-del-camice/wp-content/plugins/woocommerce-product-options/dependencies/barn2/setup-wizard/src/Steps/Welcome.php:25
[02-Jul-2025 21:18:21 UTC] PHP  14. translate($text = 'Welcome', $domain = 'woocommerce-product-options') /www/kinsta/public/bottega-del-camice/wp-includes/l10n.php:340
[02-Jul-2025 21:18:21 UTC] PHP  15. get_translations_for_domain($domain = 'woocommerce-product-options') /www/kinsta/public/bottega-del-camice/wp-includes/l10n.php:195
[02-Jul-2025 21:18:21 UTC] PHP  16. _load_textdomain_just_in_time($domain = 'woocommerce-product-options') /www/kinsta/public/bottega-del-camice/wp-includes/l10n.php:1409
[02-Jul-2025 21:18:21 UTC] PHP  17. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>woocommerce-product-options</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /www/kinsta/public/bottega-del-camice/wp-includes/l10n.php:1379
[02-Jul-2025 21:18:21 UTC] PHP  18. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-options</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added i'..., $error_level = *uninitialized*) /www/kinsta/public/bottega-del-camice/wp-includes/functions.php:6061
[02-Jul-2025 21:18:21 UTC] PHP  19. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>woocommerce-product-options</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added i'..., $error_level = 1024) /www/kinsta/public/bottega-del-camice/wp-includes/functions.php:6121
