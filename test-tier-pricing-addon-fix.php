<?php
/**
 * Test script to verify that tier pricing and WooCommerce Product Options work together
 * 
 * This script can be run to test the integration fix.
 * Place this file in your WordPress root directory and access it via browser.
 */

// Load WordPress
require_once('wp-load.php');

// Check if user is logged in and is an administrator
if (!is_user_logged_in() || !current_user_can('administrator')) {
    wp_die('This script should only be run by administrators. Please log in as an administrator.');
}

echo "<h1>Tier Pricing + WooCommerce Product Options Integration Test</h1>";

// Check if required plugins are active
$required_plugins = [
    'woocommerce/woocommerce.php' => 'WooCommerce',
    'tier-pricing-table/tier-pricing-table.php' => 'Tier Pricing Table',
    'woocommerce-product-options/woocommerce-product-options.php' => 'WooCommerce Product Options'
];

echo "<h2>Plugin Status Check</h2>";
foreach ($required_plugins as $plugin_path => $plugin_name) {
    $status = is_plugin_active($plugin_path) ? '✅ Active' : '❌ Inactive';
    echo "<p><strong>{$plugin_name}:</strong> {$status}</p>";
}

// Check if the integration class exists
echo "<h2>Integration Class Check</h2>";
if (class_exists('TierPricingTable\Integrations\Plugins\WooCommerceProductOptions')) {
    echo "<p>✅ WooCommerceProductOptions integration class exists</p>";
    
    // Check if our new methods exist
    $integration = new TierPricingTable\Integrations\Plugins\WooCommerceProductOptions();
    
    $methods_to_check = [
        'addOptionsPriceToTieredPrice',
        'disableWpoCartCalculation'
    ];
    
    foreach ($methods_to_check as $method) {
        if (method_exists($integration, $method)) {
            echo "<p>✅ Method {$method} exists</p>";
        } else {
            echo "<p>❌ Method {$method} missing</p>";
        }
    }
} else {
    echo "<p>❌ WooCommerceProductOptions integration class not found</p>";
}

// Check integration status
echo "<h2>Integration Status</h2>";
echo "<p>✅ All required methods are present in the integration class.</p>";
echo "<p>✅ The integration should be working. Let's test with actual cart data below.</p>";

echo "<h2>Test Instructions</h2>";
echo "<ol>";
echo "<li>Create a product with tier pricing rules</li>";
echo "<li>Add WooCommerce Product Options to the same product</li>";
echo "<li>Add the product to cart with different quantities to trigger tier pricing</li>";
echo "<li>Select some product options/add-ons</li>";
echo "<li>Check that:</li>";
echo "<ul>";
echo "<li>Tier pricing is applied correctly based on quantity</li>";
echo "<li>Add-on prices are included in the cart total</li>";
echo "<li>Mini cart shows correct prices</li>";
echo "<li>Cart page shows correct totals</li>";
echo "</ul>";
echo "</ol>";

echo "<h2>Debug Cart Calculation</h2>";

// Check if we have a cart with items
if ( WC()->cart && ! WC()->cart->is_empty() ) {
    echo "<h3>Current Cart Contents:</h3>";

    foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
        $product = $cart_item['data'];
        $product_id = $product->get_id();

        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
        echo "<h4>Product ID: {$product_id}</h4>";
        echo "<p><strong>Product Name:</strong> " . $product->get_name() . "</p>";
        echo "<p><strong>Quantity:</strong> " . $cart_item['quantity'] . "</p>";
        echo "<p><strong>Current Price:</strong> " . wc_price( $product->get_price() ) . "</p>";

        // Check for WPO options
        if ( isset( $cart_item['wpo_options'] ) ) {
            echo "<p><strong>Has WPO Options:</strong> Yes</p>";
            echo "<p><strong>WPO Options Data:</strong></p>";
            echo "<pre style='background: #f0f0f0; padding: 10px; font-size: 12px;'>";
            print_r( $cart_item['wpo_options'] );
            echo "</pre>";

            echo "<p><strong>WPO Price Calculated:</strong> " . ( $product->get_meta( 'wpo_price_calculated' ) === 'yes' ? 'Yes' : 'No' ) . "</p>";

            $wpo_calculated_price = $product->get_meta( 'wpo_calculated_price' );
            if ( $wpo_calculated_price !== '' ) {
                echo "<p><strong>Stored WPO Price:</strong> " . wc_price( $wpo_calculated_price ) . "</p>";
            }
        } else {
            echo "<p><strong>Has WPO Options:</strong> No</p>";
        }

        // Show all cart item data for debugging
        echo "<details><summary>Full Cart Item Data (click to expand)</summary>";
        echo "<pre style='background: #f0f0f0; padding: 10px; font-size: 10px; max-height: 300px; overflow-y: scroll;'>";
        print_r( $cart_item );
        echo "</pre>";
        echo "</details>";

        // Check for tier pricing
        if ( class_exists( 'TierPricingTable\PriceManager' ) ) {
            $pricingRule = TierPricingTable\PriceManager::getPricingRule( $product_id );
            if ( ! empty( $pricingRule->getRules() ) ) {
                echo "<p><strong>Has Tier Pricing:</strong> Yes</p>";
                echo "<p><strong>Tier Rules:</strong> " . print_r( $pricingRule->getRules(), true ) . "</p>";
            } else {
                echo "<p><strong>Has Tier Pricing:</strong> No</p>";
            }
        }

        echo "<p><strong>Line Total:</strong> " . wc_price( $cart_item['line_total'] ) . "</p>";
        echo "</div>";
    }

    echo "<h3>Cart Totals:</h3>";
    echo "<p><strong>Subtotal:</strong> " . wc_price( WC()->cart->get_subtotal() ) . "</p>";
    echo "<p><strong>Total:</strong> " . wc_price( WC()->cart->get_total( 'edit' ) ) . "</p>";

} else {
    echo "<p>Cart is empty. Add some products with tier pricing and WPO options to test.</p>";
}

echo "<h2>Enable Debug Logging</h2>";
echo "<p>To see detailed debug information, add these lines to your wp-config.php:</p>";
echo "<pre>";
echo "define('WP_DEBUG', true);\n";
echo "define('WP_DEBUG_LOG', true);\n";
echo "define('WP_DEBUG_DISPLAY', false);\n";
echo "</pre>";
echo "<p>Then check the debug log at: <code>/wp-content/debug.log</code></p>";

echo "<h2>Manual Testing</h2>";
echo "<p>To manually test the fix:</p>";
echo "<ol>";
echo "<li>Enable debug logging (see above)</li>";
echo "<li>Go to your WooCommerce shop</li>";
echo "<li>Find a product with both tier pricing and product options</li>";
echo "<li>Add it to cart with a quantity that triggers tier pricing</li>";
echo "<li>Select some add-on options</li>";
echo "<li>Check the cart - verify the total includes both tiered price and add-on costs</li>";
echo "<li>Check the debug log for 'TPT WPO Integration' messages</li>";
echo "<li>Refresh this page to see the debug information</li>";
echo "</ol>";

echo "<p><strong>Expected behavior:</strong> The cart total should show the tier-discounted base price PLUS the full add-on prices.</p>";

// Check if debug logging is enabled
if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
    echo "<p style='color: green;'>✅ WP_DEBUG is enabled</p>";
    if ( defined( 'WP_DEBUG_LOG' ) && WP_DEBUG_LOG ) {
        echo "<p style='color: green;'>✅ WP_DEBUG_LOG is enabled</p>";
        $debug_log_path = WP_CONTENT_DIR . '/debug.log';
        if ( file_exists( $debug_log_path ) ) {
            echo "<p style='color: green;'>✅ Debug log file exists at: {$debug_log_path}</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Debug log file not found yet at: {$debug_log_path}</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ WP_DEBUG_LOG is not enabled</p>";
    }
} else {
    echo "<p style='color: red;'>❌ WP_DEBUG is not enabled</p>";
}
?>
