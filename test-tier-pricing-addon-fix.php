<?php
/**
 * Test script to verify that tier pricing and WooCommerce Product Options work together
 * 
 * This script can be run to test the integration fix.
 * Place this file in your WordPress root directory and access it via browser.
 */

// Load WordPress
require_once('wp-load.php');

if (!is_admin()) {
    wp_die('This script should only be run by administrators.');
}

echo "<h1>Tier Pricing + WooCommerce Product Options Integration Test</h1>";

// Check if required plugins are active
$required_plugins = [
    'woocommerce/woocommerce.php' => 'WooCommerce',
    'tier-pricing-table/tier-pricing-table.php' => 'Tier Pricing Table',
    'woocommerce-product-options/woocommerce-product-options.php' => 'WooCommerce Product Options'
];

echo "<h2>Plugin Status Check</h2>";
foreach ($required_plugins as $plugin_path => $plugin_name) {
    $status = is_plugin_active($plugin_path) ? '✅ Active' : '❌ Inactive';
    echo "<p><strong>{$plugin_name}:</strong> {$status}</p>";
}

// Check if the integration class exists
echo "<h2>Integration Class Check</h2>";
if (class_exists('TierPricingTable\Integrations\Plugins\WooCommerceProductOptions')) {
    echo "<p>✅ WooCommerceProductOptions integration class exists</p>";
    
    // Check if our new methods exist
    $integration = new TierPricingTable\Integrations\Plugins\WooCommerceProductOptions();
    
    $methods_to_check = [
        'setTieredPriceForWpoCalculation',
        'restoreOriginalPriceAfterWpoCalculation', 
        'preventTptOverrideWpoCalculation'
    ];
    
    foreach ($methods_to_check as $method) {
        if (method_exists($integration, $method)) {
            echo "<p>✅ Method {$method} exists</p>";
        } else {
            echo "<p>❌ Method {$method} missing</p>";
        }
    }
} else {
    echo "<p>❌ WooCommerceProductOptions integration class not found</p>";
}

// Check current filter priorities
echo "<h2>Hook Priority Check</h2>";
global $wp_filter;

if (isset($wp_filter['woocommerce_before_calculate_totals'])) {
    echo "<h3>woocommerce_before_calculate_totals hooks:</h3>";
    foreach ($wp_filter['woocommerce_before_calculate_totals']->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            $callback_name = '';
            if (is_array($callback['function'])) {
                if (is_object($callback['function'][0])) {
                    $callback_name = get_class($callback['function'][0]) . '::' . $callback['function'][1];
                } else {
                    $callback_name = $callback['function'][0] . '::' . $callback['function'][1];
                }
            } else {
                $callback_name = $callback['function'];
            }
            echo "<p>Priority {$priority}: {$callback_name}</p>";
        }
    }
}

echo "<h2>Test Instructions</h2>";
echo "<ol>";
echo "<li>Create a product with tier pricing rules</li>";
echo "<li>Add WooCommerce Product Options to the same product</li>";
echo "<li>Add the product to cart with different quantities to trigger tier pricing</li>";
echo "<li>Select some product options/add-ons</li>";
echo "<li>Check that:</li>";
echo "<ul>";
echo "<li>Tier pricing is applied correctly based on quantity</li>";
echo "<li>Add-on prices are included in the cart total</li>";
echo "<li>Mini cart shows correct prices</li>";
echo "<li>Cart page shows correct totals</li>";
echo "</ul>";
echo "</ol>";

echo "<h2>Manual Testing</h2>";
echo "<p>To manually test the fix:</p>";
echo "<ol>";
echo "<li>Go to your WooCommerce shop</li>";
echo "<li>Find a product with both tier pricing and product options</li>";
echo "<li>Add it to cart with a quantity that triggers tier pricing</li>";
echo "<li>Select some add-on options</li>";
echo "<li>Verify the cart total includes both the tiered price and add-on costs</li>";
echo "</ol>";

echo "<p><strong>Expected behavior:</strong> The cart total should show the tier-discounted base price PLUS the full add-on prices.</p>";
?>
